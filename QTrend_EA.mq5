//+------------------------------------------------------------------+
//|                                                    QTrend_EA.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"

//--- Input parameters
input group "Main Settings"
input int      TrendPeriod = 200;           // Trend period
input int      ATRPeriod = 14;              // ATR Period
input double   ATRMultiplier = 1.0;         // ATR Multiplier
input bool     UseEMASmooth = false;        // Smooth source with EMA
input int      EMAPeriod = 3;               // EMA Smoother period


input group "Entry Filters"
input bool     UseEMAEntryFilter = true;   // Require EMA filter
input int      FastEMAPeriod = 7;          // Fast EMA period
input int      SlowEMAPeriod = 21;         // Slow EMA period

input group "Risk and Trade Limits"
input bool     WaitForNewCrossAfterClose = true; // Require fresh EMA cross after flat before new trade
input int      MinBarsBetweenTrades = 0;         // 0 = disabled
input int      MaxTradesPerDay = 0;              // 0 = unlimited
input double   MaxSpreadPoints = 0;              // 0 = disabled

input group "Sizing / Martingale"
input bool     UseMartingaleAfterLoss = false; // Increase lot only after a losing trade
input bool     UseRecoverySizing = true;       // Size next lot to recover last loss at TP
input int      MaxRecoverySteps = 3;           // Not strictly enforced; advisory cap


input group "Partial Close Settings"
input bool     PartialCloseOnce = true;          // Prevent repeated partial closes per position
input bool     BreakEvenAfterPartial = true;     // Move SL to breakeven after partial close
input int      BreakEvenOffsetPoints = 20;       // Extra points beyond breakeven

input group "Trading Settings"
input double   LotSize = 0.01;              // Base lot size
input bool     UseATRBasedSLTP = true;      // Use ATR-based SL/TP
input int      StopLoss = 0;                // Stop Loss in points (0 = disabled, used when ATR-based is off)
input int      TakeProfit = 0;              // Take Profit in points (0 = disabled, used when ATR-based is off)
input double   ATR_SL_Multiplier = 2.0;     // ATR multiplier for Stop Loss
input double   ATR_TP_Multiplier = 3.0;     // ATR multiplier for Take Profit
input bool     UsePartialClose = true;      // Use partial position closure
input bool     UseAutoLotSizing = false;    // Auto increase lot size on consecutive trades
input double   LotMultiplier = 2.0;         // Lot size multiplier for auto sizing
input int      MagicNumber = 12345;         // Magic number
input string   TradeComment = "QTrend_EA";  // Trade comment

//--- Global variables
int atrHandle;
int emaHandle;
double trendLine[];
string lastSignal = "";
datetime lastBarTime = 0;

// Track partial closes per ticket
ulong partialClosedTickets[];

//--- Added EMA entry filter and trade management state
int emaFastHandle = INVALID_HANDLE;
int emaSlowHandle = INVALID_HANDLE;

// Martingale/recovery tracking
bool lastClosedWasLoss = false;
double lastClosedLossMoney = 0.0;
int recoveryStep = 0;

datetime lastCrossTime = 0;
int lastCrossDir = 0; // 1 up, -1 down

// Re-entry/limits tracking
datetime lastFlatTime = 0;
datetime lastTradeBarTime = 0;
int tradesTodayCount = 0;
int dayKey = -1;
bool hadPosition = false;


//+------------------------------------------------------------------+
//| Get the appropriate filling mode for the symbol                 |
//+------------------------------------------------------------------+
ENUM_ORDER_TYPE_FILLING GetFillingMode()
{
   uint filling = (uint)SymbolInfoInteger(_Symbol, SYMBOL_FILLING_MODE);

   if((filling & SYMBOL_FILLING_FOK) == SYMBOL_FILLING_FOK)
      return ORDER_FILLING_FOK;
   else if((filling & SYMBOL_FILLING_IOC) == SYMBOL_FILLING_IOC)
      return ORDER_FILLING_IOC;
   else
      return ORDER_FILLING_RETURN;
}

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   //--- Create ATR indicator handle
   atrHandle = iATR(_Symbol, _Period, ATRPeriod);
   if(atrHandle == INVALID_HANDLE)
   {
      Print("Error creating ATR indicator");
      return(INIT_FAILED);
   }

   //--- Create EMA indicator handle if needed
   if(UseEMASmooth)
   {
      emaHandle = iMA(_Symbol, _Period, EMAPeriod, 0, MODE_EMA, PRICE_CLOSE);
      if(emaHandle == INVALID_HANDLE)
      {
         Print("Error creating EMA indicator");
         return(INIT_FAILED);
      }
   }

   //--- Create EMA handles for entry filter
   if(UseEMAEntryFilter)
   {
      emaFastHandle = iMA(_Symbol, _Period, FastEMAPeriod, 0, MODE_EMA, PRICE_CLOSE);
      emaSlowHandle = iMA(_Symbol, _Period, SlowEMAPeriod, 0, MODE_EMA, PRICE_CLOSE);
      if(emaFastHandle == INVALID_HANDLE || emaSlowHandle == INVALID_HANDLE)
      {
         Print("Error creating EMA handles for entry filter");
         return(INIT_FAILED);
      }
   }


   //--- Initialize arrays
   ArraySetAsSeries(trendLine, true);

   Print("QTrend EA initialized successfully");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |

//+------------------------------------------------------------------+
//| Trade transaction event                                          |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,const MqlTradeRequest& request,const MqlTradeResult& result)
{
   if(trans.type == TRADE_TRANSACTION_DEAL_ADD)
   {
      ulong deal_ticket = trans.deal;
      if(HistorySelect(TimeCurrent()-86400*30, TimeCurrent()))
      {
         if(HistoryDealSelect(deal_ticket))
         {
            long deal_type = HistoryDealGetInteger(deal_ticket, DEAL_TYPE);
            string sym = HistoryDealGetString(deal_ticket, DEAL_SYMBOL);
            long magic = HistoryDealGetInteger(deal_ticket, DEAL_MAGIC);

            if(sym == _Symbol && magic == MagicNumber)
            {
               if(deal_type == DEAL_TYPE_SELL || deal_type == DEAL_TYPE_BUY)
               {
                  // position open - nothing
               }
               else if(deal_type == DEAL_TYPE_SELL || deal_type == DEAL_TYPE_BUY)
               {
                  // redundant safeguard
               }
            }
         }
      }
   }

   if(trans.type == TRADE_TRANSACTION_HISTORY_ADD)
   {
      ulong deal_ticket = trans.deal;
      if(HistorySelect(TimeCurrent()-86400*5, TimeCurrent()) && HistoryDealSelect(deal_ticket))
      {
         string sym = HistoryDealGetString(deal_ticket, DEAL_SYMBOL);
         long magic = HistoryDealGetInteger(deal_ticket, DEAL_MAGIC);
         long d_entry = HistoryDealGetInteger(deal_ticket, DEAL_ENTRY);
         if(sym == _Symbol && magic == MagicNumber && d_entry == DEAL_ENTRY_OUT)
         {
            double profit = HistoryDealGetDouble(deal_ticket, DEAL_PROFIT) + HistoryDealGetDouble(deal_ticket, DEAL_COMMISSION) + HistoryDealGetDouble(deal_ticket, DEAL_SWAP);
            if(profit < 0)
            {
               lastClosedWasLoss = true;
               lastClosedLossMoney = -profit;
               recoveryStep++;
            }
            else
            {
               lastClosedWasLoss = false;
               lastClosedLossMoney = 0.0;
               recoveryStep = 0;
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   //--- Release indicator handles
   if(atrHandle != INVALID_HANDLE)
      IndicatorRelease(atrHandle);
   if(emaHandle != INVALID_HANDLE)
      IndicatorRelease(emaHandle);

   if(emaFastHandle != INVALID_HANDLE)
      IndicatorRelease(emaFastHandle);
   if(emaSlowHandle != INVALID_HANDLE)
      IndicatorRelease(emaSlowHandle);

}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   //--- Check if new bar
   datetime currentBarTime = iTime(_Symbol, _Period, 0);
   if(currentBarTime == lastBarTime)
      return;
   lastBarTime = currentBarTime;

   //--- Calculate indicator values
   if(!CalculateIndicator())
      return;


   //--- Reset daily trade counter on new day
   MqlDateTime dt; TimeToStruct(currentBarTime, dt);
   int dk = (int)(dt.year * 10000 + dt.mon * 100 + dt.day);
   if(dayKey != dk)
   {
      dayKey = dk;
      tradesTodayCount = 0;
   }

   //--- Detect flat transition (all positions closed)
   bool hasPos = false;
   if(PositionSelect(_Symbol))
   {
      long pmag = (long)PositionGetInteger(POSITION_MAGIC);
      if(pmag == MagicNumber) hasPos = true;
   }
   if(hadPosition && !hasPos)
   {
      lastFlatTime = currentBarTime;
      ArrayResize(partialClosedTickets, 0);
   }
   hadPosition = hasPos;

   //--- Track EMA cross time for re-entry lock
   if(UseEMAEntryFilter)
   {
      double f0[], s0[];
      ArraySetAsSeries(f0, true);
      ArraySetAsSeries(s0, true);
      if(CopyBuffer(emaFastHandle, 0, 0, 2, f0) == 2 && CopyBuffer(emaSlowHandle, 0, 0, 2, s0) == 2)
      {
         bool crossUp = (f0[1] <= s0[1]) && (f0[0] > s0[0]);
         bool crossDn = (f0[1] >= s0[1]) && (f0[0] < s0[0]);
         if(crossUp)
         {
            lastCrossTime = currentBarTime;
            lastCrossDir = 1;
         }
         else if(crossDn)
         {
            lastCrossTime = currentBarTime;
            lastCrossDir = -1;
         }
      }
   }

   //--- Check for trading signals
   CheckTradingSignals();

   //--- Manage existing positions
   ManagePositions();
}

//+------------------------------------------------------------------+
//| Calculate Q-Trend indicator values                               |
//+------------------------------------------------------------------+
bool CalculateIndicator()
{
   //--- Get required bars
   if(Bars(_Symbol, _Period) < TrendPeriod + 10)
      return false;

   //--- Get ATR values
   double atrValues[];
   ArraySetAsSeries(atrValues, true);
   if(CopyBuffer(atrHandle, 0, 1, 2, atrValues) < 2)
      return false;

   //--- Get source prices
   double src[];
   ArraySetAsSeries(src, true);

   if(UseEMASmooth)
   {
      if(CopyBuffer(emaHandle, 0, 0, TrendPeriod + 10, src) < TrendPeriod + 10)
         return false;
   }
   else
   {
      if(CopyClose(_Symbol, _Period, 0, TrendPeriod + 10, src) < TrendPeriod + 10)
         return false;
   }

   //--- Calculate highest and lowest
   double highest = src[ArrayMaximum(src, 0, TrendPeriod)];
   double lowest = src[ArrayMinimum(src, 0, TrendPeriod)];
   double range = highest - lowest;

   //--- Calculate initial trend line
   double currentTrendLine = (highest + lowest) / 2.0;

   //--- Get previous trend line value
   ArrayResize(trendLine, ArraySize(trendLine) + 1);
   if(ArraySize(trendLine) > 1)
      currentTrendLine = trendLine[1];

   //--- Calculate epsilon (sensitivity measure)
   double epsilon = ATRMultiplier * atrValues[0];

   //--- Check for trend line changes (Type A logic)
   bool changeUp = (src[0] > currentTrendLine + epsilon) ||
                   (src[1] <= currentTrendLine + epsilon && src[0] > currentTrendLine + epsilon);
   bool changeDown = (src[0] < currentTrendLine - epsilon) ||
                     (src[1] >= currentTrendLine - epsilon && src[0] < currentTrendLine - epsilon);

   //--- Update trend line
   if(changeUp)
      currentTrendLine = currentTrendLine + epsilon;
   else if(changeDown)
      currentTrendLine = currentTrendLine - epsilon;

   //--- Store current trend line value
   trendLine[0] = currentTrendLine;

   //--- Check for strong signals
   double openPrice = iOpen(_Symbol, _Period, 0);
   bool strongBuy = false;
   bool strongSell = false;

   for(int i = 0; i < 5; i++)
   {
      double pastOpen = iOpen(_Symbol, _Period, i);
      if(pastOpen < lowest + range / 8.0 && pastOpen >= lowest)
         strongBuy = true;
      if(pastOpen > highest - range / 8.0 && pastOpen <= highest)
         strongSell = true;
   }

   //--- Update last signal
   if(changeUp && !strongBuy)
      lastSignal = "BUY";
   else if(changeUp && strongBuy)
      lastSignal = "STRONG_BUY";
   else if(changeDown && !strongSell)
      lastSignal = "SELL";
   else if(changeDown && strongSell)
      lastSignal = "STRONG_SELL";

   return true;
}

//+------------------------------------------------------------------+
//| Check for trading signals                                        |
//+------------------------------------------------------------------+
void CheckTradingSignals()
{
   //--- Check if we have a new signal
   static string prevSignal = "";

   // Spread filter
   if(MaxSpreadPoints > 0)
   {
      double spreadPts = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID)) / _Point;
      if(spreadPts > MaxSpreadPoints)
         return;
   }

   // Trade limits: per day and min bars
   if(MaxTradesPerDay > 0 && tradesTodayCount >= MaxTradesPerDay)
      return;
   if(MinBarsBetweenTrades > 0 && lastTradeBarTime > 0)
   {
      int bars_since = iBarShift(_Symbol, _Period, lastTradeBarTime, true);
      if(bars_since >= 0 && bars_since < MinBarsBetweenTrades)
         return;
   }

   // EMA entry filter gating and re-entry lock
   int desiredDir = 0; // 1 buy, -1 sell
   if(StringFind(lastSignal, "BUY") >= 0) desiredDir = 1;
   else if(StringFind(lastSignal, "SELL") >= 0) desiredDir = -1;

   if(desiredDir == 0 || lastSignal == prevSignal || lastSignal == "")
      return;

   if(UseEMAEntryFilter)
   {
     double f0[], s0[], f1[], s1[];
     ArraySetAsSeries(f0, true);
     ArraySetAsSeries(s0, true);
     ArraySetAsSeries(f1, true);
     ArraySetAsSeries(s1, true);
     if(CopyBuffer(emaFastHandle, 0, 0, 1, f0) < 1 || CopyBuffer(emaSlowHandle, 0, 0, 1, s0) < 1)
        return;
     if(CopyBuffer(emaFastHandle, 0, 1, 1, f1) < 1 || CopyBuffer(emaSlowHandle, 0, 1, 1, s1) < 1)
        return;

     // EMA relationship
     bool ema_ok = (desiredDir == 1) ? (f0[0] > s0[0]) : (f0[0] < s0[0]);
     if(!ema_ok) return;

     // Price close filter on completed bar
     double close1 = iClose(_Symbol, _Period, 1);
     bool price_ok = (desiredDir == 1) ? (close1 > f1[0] && close1 > s1[0]) : (close1 < f1[0] && close1 < s1[0]);
     if(!price_ok) return;

     if(WaitForNewCrossAfterClose && lastFlatTime > 0)
     {
        // Require that after last flat, a new cross in the same direction happened
        if(!(lastCrossTime >= lastFlatTime && ((desiredDir == 1 && lastCrossDir == 1) || (desiredDir == -1 && lastCrossDir == -1))))
           return;
     }
   }

   //--- Close opposite positions first
   if(desiredDir == 1)
      ClosePositions(POSITION_TYPE_SELL);
   else if(desiredDir == -1)
      ClosePositions(POSITION_TYPE_BUY);

   //--- Open new position
   if(desiredDir == 1)
      OpenPosition(ORDER_TYPE_BUY);
   else if(desiredDir == -1)
      OpenPosition(ORDER_TYPE_SELL);

   lastTradeBarTime = iTime(_Symbol, _Period, 0);
   tradesTodayCount++;
   prevSignal = lastSignal;
}

//+------------------------------------------------------------------+
//| Open trading position                                            |
//+------------------------------------------------------------------+
void OpenPosition(ENUM_ORDER_TYPE orderType)
{
   MqlTradeRequest request = {};
   MqlTradeResult result = {};

   request.action = TRADE_ACTION_DEAL;
   request.symbol = _Symbol;
   request.volume = CalculateLotSize();
   request.type = orderType;
   request.price = (orderType == ORDER_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) :
                                                   SymbolInfoDouble(_Symbol, SYMBOL_BID);
   request.deviation = 3;
   request.magic = MagicNumber;
   request.comment = TradeComment;
   request.type_filling = GetFillingMode();

   //--- Set stop loss and take profit
   if(UseATRBasedSLTP)
   {
      //--- Get current ATR value
      double atrValues[];
      ArraySetAsSeries(atrValues, true);
      if(CopyBuffer(atrHandle, 0, 1, 1, atrValues) > 0)
      {
         double atrValue = atrValues[0];

         if(orderType == ORDER_TYPE_BUY)
         {
            request.sl = request.price - (ATR_SL_Multiplier * atrValue);
            request.tp = request.price + (ATR_TP_Multiplier * atrValue);
         }
         else
         {
            request.sl = request.price + (ATR_SL_Multiplier * atrValue);
            request.tp = request.price - (ATR_TP_Multiplier * atrValue);
         }
      }
   }
   else
   {
      //--- Use fixed points SL/TP
      if(StopLoss > 0)
      {
         if(orderType == ORDER_TYPE_BUY)
            request.sl = request.price - StopLoss * _Point;
         else
            request.sl = request.price + StopLoss * _Point;
      }

      if(TakeProfit > 0)
      {
         if(orderType == ORDER_TYPE_BUY)
            request.tp = request.price + TakeProfit * _Point;
         else
            request.tp = request.price - TakeProfit * _Point;
      }
   }

   //--- Send order
   bool orderResult = OrderSend(request, result);
   if(orderResult && result.retcode == TRADE_RETCODE_DONE)
   {
      Print("Position opened: ", EnumToString(orderType), " at ", request.price,
            " | Volume: ", request.volume, " | SL: ", request.sl, " | TP: ", request.tp);

      // reset partial-close memory for this ticket if present
      ulong op_ticket = (ulong)result.order;
      if(op_ticket > 0)
      {
         for(int k = ArraySize(partialClosedTickets)-1; k >= 0; k--)
         {
            if(partialClosedTickets[k] == op_ticket)
            {
               // remove by swapping with last
               int lastIdx = ArraySize(partialClosedTickets)-1;
               partialClosedTickets[k] = partialClosedTickets[lastIdx];
               ArrayResize(partialClosedTickets, lastIdx);
               break;
            }
         }
      }
   }
   else
   {
      Print("Error opening position: ", result.retcode, " - ", result.comment);
   }
}

//+------------------------------------------------------------------+
//| Close positions of specified type                               |
//+------------------------------------------------------------------+
void ClosePositions(ENUM_POSITION_TYPE posType)
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(!PositionSelect(_Symbol)) continue;
      string psym = PositionGetString(POSITION_SYMBOL);
      long   pmag = (long)PositionGetInteger(POSITION_MAGIC);
      long   ptype= (long)PositionGetInteger(POSITION_TYPE);
      if(psym == _Symbol && pmag == MagicNumber && ptype == posType)
      {
         MqlTradeRequest request = {};
         MqlTradeResult result = {};

         request.action = TRADE_ACTION_DEAL;
         request.symbol = _Symbol;
         request.volume = PositionGetDouble(POSITION_VOLUME);
         request.type = (posType == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
         request.price = (posType == POSITION_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                                                          SymbolInfoDouble(_Symbol, SYMBOL_ASK);
         request.position = (ulong)PositionGetInteger(POSITION_TICKET);
         request.deviation = 3;
         request.magic = MagicNumber;
         request.type_filling = GetFillingMode();

         bool closeResult = OrderSend(request, result);
         if(!closeResult || result.retcode != TRADE_RETCODE_DONE)
         {
            Print("Error closing position: ", result.retcode, " - ", result.comment);
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Manage existing positions                                        |
//+------------------------------------------------------------------+
void ManagePositions()
{
   if(!UsePartialClose)
      return;

   //--- Check positions for partial closure
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(!PositionSelect(_Symbol)) continue;
      string psym = PositionGetString(POSITION_SYMBOL);
      long   pmag = (long)PositionGetInteger(POSITION_MAGIC);
      if(psym == _Symbol && pmag == MagicNumber)
      {
         double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
         double currentPrice = PositionGetDouble(POSITION_PRICE_CURRENT);
         double takeProfit = PositionGetDouble(POSITION_TP);
         double volume = PositionGetDouble(POSITION_VOLUME);
         long ticket = (long)PositionGetInteger(POSITION_TICKET);
         ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

         if(takeProfit > 0 && volume > 0.01) // Only if TP is set and volume allows partial close
         {
            // Skip if we already partially closed this ticket
            bool alreadyClosed = false;
            if(PartialCloseOnce)
            {
               for(int k = 0; k < ArraySize(partialClosedTickets); k++)
               {
                  if(partialClosedTickets[k] == (ulong)ticket)
                  {
                     alreadyClosed = true;
                     break;
                  }
               }
            }
            if(alreadyClosed) continue;

            double halfwayPrice;
            bool shouldPartialClose = false;

            if(posType == POSITION_TYPE_BUY)
            {
               halfwayPrice = openPrice + (takeProfit - openPrice) / 2.0;
               shouldPartialClose = (currentPrice >= halfwayPrice);
            }
            else
            {
               halfwayPrice = openPrice - (openPrice - takeProfit) / 2.0;
               shouldPartialClose = (currentPrice <= halfwayPrice);
            }

            if(shouldPartialClose)
            {
               // Close half position
               MqlTradeRequest request = {};
               MqlTradeResult result = {};

               request.action = TRADE_ACTION_DEAL;
               request.symbol = _Symbol;
               request.volume = volume / 2.0;
               request.type = (posType == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
               request.price = (posType == POSITION_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                                                               SymbolInfoDouble(_Symbol, SYMBOL_ASK);
               request.position = (ulong)ticket;
               request.deviation = 3;
               request.magic = MagicNumber;
               request.comment = "Partial Close";
               request.type_filling = GetFillingMode();

               bool partialResult = OrderSend(request, result);
               if(partialResult && result.retcode == TRADE_RETCODE_DONE)
               {
                  Print("Partial close executed: ", request.volume, " lots at ", request.price);

                  // Mark ticket as partially closed
                  if(PartialCloseOnce)
                  {
                     int n = ArraySize(partialClosedTickets);
                     ArrayResize(partialClosedTickets, n + 1);
                     partialClosedTickets[n] = (ulong)ticket;
                  }

                  // Move SL to breakeven if requested
                  if(BreakEvenAfterPartial)
                  {
                     double newSL = openPrice;
                     if(posType == POSITION_TYPE_BUY)
                        newSL = openPrice + BreakEvenOffsetPoints * _Point;
                     else
                        newSL = openPrice - BreakEvenOffsetPoints * _Point;

                     MqlTradeRequest mod = {};
                     MqlTradeResult  res = {};
                     mod.action  = TRADE_ACTION_SLTP;
                     mod.symbol  = _Symbol;
                     mod.position= ticket;
                     mod.sl      = newSL;
                     mod.tp      = takeProfit; // keep TP unchanged
                     bool m = OrderSend(mod, res);
                     if(m && res.retcode == TRADE_RETCODE_DONE)
                        Print("Moved SL to breakeven+ after partial close: ", newSL);
                     else
                        Print("Failed to move SL to breakeven: ", res.retcode, " - ", res.comment);
                  }
               }
               else
               {
                  Print("Error in partial close: ", result.retcode, " - ", result.comment);
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Calculate lot size based on auto sizing settings                |
//+------------------------------------------------------------------+
double CalculateLotSize()
{
   // Base lot if no auto sizing features enabled
   if(!UseAutoLotSizing && !UseMartingaleAfterLoss && !UseRecoverySizing)
      return LotSize;

   double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

   double lot = LotSize;

   // Simple progressive sizing based on last floating position (original behavior)
   if(UseAutoLotSizing)
   {
      double lastPositionVolume = 0.0;
      datetime lastPositionTime = 0;
      for(int i = 0; i < PositionsTotal(); i++)
      {
         if(!PositionSelect(_Symbol)) continue;
         string psym = PositionGetString(POSITION_SYMBOL);
         long   pmag = (long)PositionGetInteger(POSITION_MAGIC);
         if(psym == _Symbol && pmag == MagicNumber)
         {
            datetime posTime = (datetime)PositionGetInteger(POSITION_TIME);
            if(posTime > lastPositionTime)
            {
               lastPositionTime = posTime;
               lastPositionVolume = PositionGetDouble(POSITION_VOLUME);
            }
         }
      }
      if(lastPositionVolume > 0.0)
         lot = MathMax(lot, lastPositionVolume * LotMultiplier);
   }

   // Martingale only after a losing close
   if(UseMartingaleAfterLoss && lastClosedWasLoss)
   {
      lot = MathMax(lot, LotSize * MathPow(LotMultiplier, MathMin(recoveryStep, MaxRecoverySteps)));
   }

   // Recovery sizing: size to recover last loss at TP distance
   if(UseRecoverySizing && lastClosedWasLoss && lastClosedLossMoney > 0.0)
   {
      // Approximate TP distance in points based on settings
      double tp_points = 0.0;
      if(UseATRBasedSLTP)
      {
         double atrValues[]; ArraySetAsSeries(atrValues, true);
         if(CopyBuffer(atrHandle, 0, 1, 1, atrValues) > 0)
            tp_points = (ATR_TP_Multiplier * atrValues[0]) / _Point;
      }
      else if(TakeProfit > 0)
      {
         tp_points = TakeProfit;
      }
      // pip value per lot approximation
      double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
      double tickSize  = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
      double pointValuePerLot = (tickValue / tickSize) * _Point; // money per point at 1 lot

      if(tp_points > 0 && pointValuePerLot > 0)
      {
         double lots_needed = lastClosedLossMoney / (tp_points * pointValuePerLot);
         lot = MathMax(lot, lots_needed);
      }
   }

   // Normalize to lot step and clamp to limits
   lot = MathFloor(lot / lotStep + 1e-8) * lotStep;
   lot = MathMax(lot, minLot);
   lot = MathMin(lot, maxLot);

   return lot;
}
